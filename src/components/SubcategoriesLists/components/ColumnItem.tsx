import {useTranslation} from 'react-i18next';
import {FlatList, ImageBackground, ScrollView, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import Animated, {FadeInLeft} from 'react-native-reanimated';
import {createStyles} from '../styles';
import RenderItem from './RenderItem';
import {CategoryType, SubCategoryType} from '~types/categories';
import {FC, useEffect, useState} from 'react';
import DownIcon from '~assets/icons/DownIcon';
import UpIcon from '~assets/icons/UpIcon';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  category: string;
  items: SubCategoryType[];
  categoryIndex: number;
  setCategories: (subcategory: number | number[]) => void;
  selectedSubCats: number[];
  categoriesList?: CategoryType[];
  isPopular?: boolean;
  isExpand?: boolean;
  onCategoryClick: (index: number) => void;
  searchValue?: string;
  setCategoriesAll?: (subcategory: number | number[]) => void;
}

const ColumnItem: FC<IProps> = ({
  category,
  items,
  categoryIndex,
  categoriesList,
  setCategories,
  selectedSubCats,
  isPopular,
  isExpand,
  onCategoryClick,
  searchValue,
  setCategoriesAll,
}) => {
  const {t} = useTranslation();
  const [isAllSelected, setIsAllSelected] = useState(false);

  const {colors} = useTheme();
  const styles = createStyles(colors);

  const handleTranslation = (translationText: string) => {
    if (t(`categories.${translationText}`) === `categories.${translationText}`) {
      console.log(translationText);
      return (
        categoriesList?.find(categoryItem => translationText === categoryItem.category_name)?.category_repr ||
        translationText
      );
    }
    return t(`categories.${translationText}`);
  };

  // Use the global selectedSubCats instead of local state
  useEffect(() => {
    if (setCategoriesAll) {
      setCategoriesAll(selectedSubCats);
    }
  }, [selectedSubCats, setCategoriesAll]);

  const handlePress = (subcategoryId: number) => {
    setCategories(subcategoryId);
  };

  return (
    <View key={category}>
      {isPopular && (
        <TouchableOpacity onPress={() => onCategoryClick(categoryIndex)}>
          <View style={styles.imageView}>
            <ImageBackground
              source={{
                uri: categoriesList?.find(categoryItem => category === categoryItem.category_name)?.image_url,
              }}
              style={styles.image}>
              <View style={styles.overlay} />
              <View style={styles.imageContainer}>
                <View>
                  <Text style={styles.subCatTitle}>{handleTranslation(category)}</Text>
                  <Text style={styles.subCatDesc}>{`${items.length} subcategories`}</Text>
                </View>
                {isExpand || searchValue ? <UpIcon /> : <DownIcon />}
              </View>
            </ImageBackground>
          </View>
        </TouchableOpacity>
      )}

      {!isPopular && (
        <TouchableOpacity style={styles.categoryView} onPress={() => onCategoryClick(categoryIndex)}>
          <Text style={styles.categoryText}>{handleTranslation(category)}</Text>
          {isExpand || searchValue ? (
            <UpIcon color={colors.textSecondary} />
          ) : (
            <DownIcon color={colors.textSecondary} />
          )}
        </TouchableOpacity>
      )}

      {/* old design hide */}
      {false && (
        <TouchableOpacity
          onPress={() => {
            setIsAllSelected(prevState => !prevState);
            items.forEach(item => {
              if (!isAllSelected && selectedSubCats.includes(item.subcategory_id)) {
                return;
              }
              if (isAllSelected && !selectedSubCats.includes(item.subcategory_id)) {
                return;
              }
              handlePress(item.subcategory_id);
            });
          }}>
          <Animated.Text entering={FadeInLeft.duration(300)} style={styles.header}>
            {handleTranslation(category)}
          </Animated.Text>
        </TouchableOpacity>
      )}

      {(isExpand || searchValue) && (
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{marginVertical: 12}}>
          <FlatList
            scrollEnabled={false}
            numColumns={20}
            contentContainerStyle={{paddingLeft: 12}}
            nestedScrollEnabled={true}
            keyboardShouldPersistTaps="always"
            data={items}
            keyExtractor={item => `key-${item.subcategory_id}`}
            renderItem={({item}) => (
              <RenderItem
                item={item}
                selectedSubCats={selectedSubCats}
                handlePress={handlePress}
                categoryIndex={categoryIndex}
                itemsLength={items?.length || 1}
              />
            )}
          />
        </ScrollView>
      )}
    </View>
  );
};

export default ColumnItem;

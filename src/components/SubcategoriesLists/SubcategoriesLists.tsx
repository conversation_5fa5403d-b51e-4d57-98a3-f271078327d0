import {FC, useEffect, useMemo, useState} from 'react';
import {FlatList, ScrollView, Text, TouchableOpacity, View, useWindowDimensions} from 'react-native';
import {CategoryType, SubCategoryType} from '~types/categories';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import {useOnboardingStore} from '~providers/onboarding/zustand';
import {useUpdateUserStore} from '~providers/updateUser/zustand';
import {useEventCreationStore} from '~providers/eventCreation/zustand';
import {FlashList} from '@shopify/flash-list';
import ColumnItem from './components/ColumnItem';
import {createStyles} from './styles';
import SmallUpIcon from '~assets/icons/SmallUpIcon';
import SmallDownIcon from '~assets/icons/SmallDownIcon';
import RenderItem from './components/RenderItem';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useTheme} from '~contexts/ThemeContext';

interface IProps {
  filteredSubcategoriesData: Record<string, SubCategoryType[]> | null;
  isFromSettings?: boolean;
  isFromEventCreation?: boolean;
  selectedCategories?: number[];
  categoriesList?: CategoryType[];
  searchValue?: string;
  onCategoryChange?: (subcategory: number) => void;
}

const SubcategoriesLists: FC<IProps> = ({
  filteredSubcategoriesData,
  isFromSettings = false,
  isFromEventCreation = false,
  selectedCategories,
  categoriesList,
  onCategoryChange,
  searchValue,
}) => {
  const {setPersonalSubCategories, subcategories: selectedSubCategories} = useOnboardingStore();
  const {setSubCategory, subcategories: selectedBySettingsCategories} = useUpdateUserStore();
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [selectedSubCat, setSelectedSubCatIndex] = useState<number[]>([]);
  const [pastCat, setPastCat] = useState<SubCategoryType[]>([]);
  const [isPopular, setIsPopular] = useState(true);
  const {subcategory: eventCreationSubcategory, setSubCategory: setEventCreationSubcategory} = useEventCreationStore();

  const selectedSubCats = useMemo(
    () =>
      selectedCategories
        ? selectedCategories
        : isFromSettings
          ? selectedBySettingsCategories
          : isFromEventCreation
            ? eventCreationSubcategory
            : selectedSubCategories,
    [
      eventCreationSubcategory,
      isFromEventCreation,
      isFromSettings,
      selectedBySettingsCategories,
      selectedSubCategories,
      selectedCategories,
    ],
  );

  const setCategories = onCategoryChange
    ? onCategoryChange
    : isFromSettings
      ? setSubCategory
      : isFromEventCreation
        ? setEventCreationSubcategory
        : setPersonalSubCategories;

  const {width} = useWindowDimensions();

  const sortedSubcategories = useMemo(() => {
    if (filteredSubcategoriesData) {
      let entries = Object?.entries(filteredSubcategoriesData);
      return entries;
    }
  }, [filteredSubcategoriesData]);

  const {colors} = useTheme();
  const styles = createStyles(colors);

  const selectedCatList = () => {
    let selectedCat: SubCategoryType[] = [];
    sortedSubcategories?.map(item => {
      console.log(selectedSubCats, 'selectedSubCatsselectedSubCats', item);
      // BUG-034 FIX: Use selectedSubCats instead of selectedSubCat and properly filter selected items
      const selectedCatS = item[1].filter(itemS => selectedSubCats.includes(itemS.subcategory_id));
      selectedCat = [...selectedCat, ...selectedCatS];
    });

    return selectedCat;
  };

  const getPastCatList = async () => {
    const pastSub = await AsyncStorage.getItem('subcategory');
    const subCatList = JSON.parse(pastSub || '[]');
    let selectedCat: SubCategoryType[] = [];
    sortedSubcategories?.map(item => {
      const selectedCatS = item[1].filter(itemS => {
        return subCatList.includes(itemS.subcategory_id);
      });
      selectedCat = [...selectedCat, ...selectedCatS];
    });
    setPastCat(selectedCat);
  };

  useEffect(() => {
    if (sortedSubcategories && sortedSubcategories.length > 0) {
      getPastCatList();
    }
  }, [sortedSubcategories]);

  if (!sortedSubcategories?.length) {
    return (
      <View style={{flex: 1}}>
        <SkeletonPlaceholder>
          <SkeletonPlaceholder.Item paddingLeft={22} marginTop={10}>
            <SkeletonPlaceholder.Item height={30} width={200} borderRadius={10} marginTop={10} />
            <SkeletonPlaceholder.Item height={100} borderRadius={10} marginTop={8} width={'95%'} />
            <SkeletonPlaceholder.Item height={30} width={200} borderRadius={10} marginTop={10} />
            <SkeletonPlaceholder.Item height={100} borderRadius={10} marginTop={8} width={'95%'} />
            <SkeletonPlaceholder.Item height={30} width={200} borderRadius={10} marginTop={10} />
            <SkeletonPlaceholder.Item height={100} borderRadius={10} marginTop={8} width={'95%'} />
          </SkeletonPlaceholder.Item>
        </SkeletonPlaceholder>
      </View>
    );
  }

  const getPopularItem = (array: [string, SubCategoryType[]][]) => {
    const arrayList = array.slice(0, 4);
    arrayList.push(array[7]);
    return arrayList;
  };

  const getOtherCat = (array: [string, SubCategoryType[]][]) => {
    const arrayList = array.slice(4, array.length);
    return arrayList.filter(
      item =>
        Array.isArray(item) && item.length >= 2 && item[1] && Array.isArray(item[1]) && item[1][0]?.category_id !== 9,
    );
  };

  const onCategoryClick = (index: number) => {
    if (selectedIndex === index) {
      setSelectedIndex(-1);
    } else {
      setSelectedIndex(index);
    }
  };

  return (
    <FlashList
      data={
        isPopular
          ? getPopularItem(sortedSubcategories)
          : [...getPopularItem(sortedSubcategories), ...getOtherCat(sortedSubcategories)]
      }
      estimatedItemSize={300}
      estimatedListSize={{width, height: 2000}}
      nestedScrollEnabled={true}
      ListHeaderComponent={() => {
        return (
          <View>
            <FlatList
              scrollEnabled={true}
              horizontal
              contentContainerStyle={{paddingLeft: 12}}
              nestedScrollEnabled={true}
              keyboardShouldPersistTaps="always"
              data={selectedCatList()}
              keyExtractor={item => `key-${item.subcategory_id}`}
              renderItem={({item, index}) => (
                <RenderItem
                  item={item}
                  selectedSubCats={selectedSubCats}
                  handlePress={() => setCategories(item.subcategory_id)} // BUG-032 FIX: Enable unselection in search view
                  categoryIndex={index}
                  itemsLength={1}
                />
              )}
            />
            {pastCat && pastCat.length > 0 && (
              <Text
                style={[styles.subCatTitle, {color: colors.textPrimary, fontSize: 16, marginLeft: 20, marginTop: 10}]}>
                Suggested Categories
              </Text>
            )}
            {pastCat && pastCat.length > 0 && (
              <FlatList
                scrollEnabled={true}
                horizontal
                contentContainerStyle={{paddingLeft: 12}}
                nestedScrollEnabled={true}
                keyboardShouldPersistTaps="always"
                data={pastCat}
                keyExtractor={item => `key-${item.subcategory_id}`}
                renderItem={({item, index}) => (
                  <RenderItem
                    item={item}
                    selectedSubCats={selectedSubCats}
                    handlePress={() => setCategories(item.subcategory_id)}
                    categoryIndex={index}
                    itemsLength={1}
                  />
                )}
              />
            )}
          </View>
        );
      }}
      extraData={selectedIndex}
      showsVerticalScrollIndicator={false}
      renderItem={({item, index: categoryIndex}) => {
        // Safety check to ensure item is an array before destructuring
        if (!Array.isArray(item) || item.length < 2) {
          console.warn('SubcategoriesLists: Invalid item structure', item);
          return null;
        }

        const [category, items] = item;

        return (
          <>
            {items?.length > 0 && (
              <ColumnItem
                category={category}
                items={items}
                isPopular={categoryIndex < 5}
                categoriesList={categoriesList}
                categoryIndex={categoryIndex}
                selectedSubCats={selectedSubCats}
                isExpand={selectedIndex === categoryIndex}
                setCategories={setCategories}
                searchValue={searchValue}
                onCategoryClick={onCategoryClick}
                setCategoriesAll={data => setSelectedSubCatIndex(data as number[])}
              />
            )}
            {categoryIndex === 4 && (
              <TouchableOpacity style={styles.moreCatView} onPress={() => setIsPopular(!isPopular)}>
                <Text style={styles.moreCatText}>More categories</Text>
                {isPopular ? <SmallDownIcon /> : <SmallUpIcon />}
              </TouchableOpacity>
            )}
          </>
        );
      }}
    />
  );
};

export default SubcategoriesLists;

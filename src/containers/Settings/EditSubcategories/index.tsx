import auth from '@react-native-firebase/auth';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useEffect, useLayoutEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, Text, TextInput, View, ScrollView} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {organizeByCategory} from '~Utils/filterSubcategories';
import {GoBackHeader} from '~components/GoBackHeader';
import SubcategoriesLists from '~components/SubcategoriesLists';
import ModernCard from '~components/ModernCard/ModernCard';
import ModernButton from '~components/ModernButton';
import {spacing, typography} from '~constants/design';
import Animated, {FadeInDown} from 'react-native-reanimated';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetCategories} from '~hooks/subcategories/useGetCategories';
import {useGetSubcategories} from '~hooks/subcategories/useGetSubcategories';
import {useGetUserSubcategories} from '~hooks/user/useGetUserSubcategories';
import {useUpdateUserSubcategories} from '~hooks/user/useUpdateUserSubcategories';
import {SubCategoryType} from '~types/categories';
import getStyles from './styles';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';

const isAndroid = Platform.OS === 'android';
const ANDROID_MARGIN_BOTTOM = 20;

const EditSubcategories = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const {data} = useGetSubcategories();
  const {data: d} = useGetCategories();
  const {mutateAsync: updateUserSubcategories} = useUpdateUserSubcategories();
  const {data: userSubcategories, isLoading: isUserSabcategoriesLoading} = useGetUserSubcategories(
    auth().currentUser?.uid || '',
  );
  const {goBack} = useNavigation();
  const {setIsTabBarDisabled} = useTabBar();

  const [categories, setCategories] = useState<Record<string, SubCategoryType[]> | null>(null);
  const [selectedSubcategories, setSelectedSubcategories] = useState<number[]>([]);
  const [searchValue, setSearchValue] = useState('');

  const [isLoading, setIsLoading] = useState(false);
  const isFocused = useIsFocused();
  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useLayoutEffect(() => {
    setIsTabBarDisabled(true);
  }, [setIsTabBarDisabled]);

  useEffect(() => {
    if (d && data && !searchValue) {
      setCategories(organizeByCategory([], d, data));
    }
  }, [d, data, searchValue]);

  useEffect(() => {
    if (userSubcategories) {
      setSelectedSubcategories(userSubcategories.map(item => item.subcategory_id));
    }
  }, [userSubcategories]);

  useEffect(() => {
    logScreenView('Edit Subcategories', 'EditSubcategories');
  }, []);

  const submit = async () => {
    setIsLoading(true);
    try {
      await updateUserSubcategories({ids: selectedSubcategories});
      goBack();
    } catch (E) {
      setIsLoading(false);
      console.log(E);
    }
  };

  useEffect(() => {
    handleSearchCategory();
  }, [searchValue]);

  const handleSearchCategory = () => {
    if (searchValue?.length && data && d) {
      const inputData = data?.filter(
        (item: any) => item?.subcategory_name?.toLowerCase().indexOf(searchValue.trim().toLowerCase()) !== -1,
      );
      // BUG-031 FIX: Include selected categories in search results
      setCategories(organizeByCategory(selectedBySettingsCategories, d, inputData));
    } else if (data && d) {
      setCategories(organizeByCategory(selectedBySettingsCategories, d, data));
    }
  };

  return (
    <>
      <GoBackHeader />
      <View style={{flex: 1, backgroundColor: colors.background}}>
        <ScrollView
          style={{flex: 1}}
          contentContainerStyle={{
            paddingHorizontal: spacing.md,
            paddingTop: spacing.xl,
            paddingBottom: spacing['6xl'],
          }}
          showsVerticalScrollIndicator={false}>
          {/* Header */}
          <Animated.View entering={FadeInDown.delay(100).duration(400)}>
            <Text
              style={{
                fontSize: typography.fontSize['3xl'],
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                marginBottom: spacing.md,
              }}>
              {t('settings.edit_interests')}
            </Text>
            <Text
              style={{
                fontSize: typography.fontSize.base,
                fontWeight: typography.fontWeight.normal,
                color: colors.textSecondary,
                lineHeight: typography.fontSize.base * 1.5,
                marginBottom: spacing.lg,
              }}>
              {t('settings.interests_description') ||
                'Select your interests to get personalized event recommendations.'}
            </Text>
          </Animated.View>

          {/* Search Section */}
          <Animated.View entering={FadeInDown.delay(200).duration(400)}>
            <ModernCard variant="default" padding="lg" margin="sm">
              <Text
                style={{
                  fontSize: typography.fontSize.lg,
                  fontWeight: typography.fontWeight.semibold,
                  color: colors.textPrimary,
                  marginBottom: spacing.md,
                }}>
                {t('generic.search') || 'Search'}
              </Text>

              <View style={{flexDirection: 'row', gap: spacing.sm}}>
                <TextInput
                  placeholder={t('settings.search_interests') || 'Search interests...'}
                  value={searchValue}
                  onChangeText={setSearchValue}
                  style={{
                    flex: 1,
                    height: 48,
                    borderWidth: 1,
                    borderColor: colors.border,
                    borderRadius: spacing.md,
                    paddingHorizontal: spacing.md,
                    fontSize: typography.fontSize.base,
                    color: colors.textPrimary,
                    backgroundColor: colors.surface,
                  }}
                  placeholderTextColor={colors.textSecondary}
                />
                <ModernButton
                  title={t('generic.search') || 'Search'}
                  onPress={handleSearchCategory}
                  variant="primary"
                  size="medium"
                  style={{minWidth: 80}}
                />
              </View>
            </ModernCard>
          </Animated.View>

          {/* Interests List */}
          <Animated.View entering={FadeInDown.delay(300).duration(400)}>
            <ModernCard variant="default" padding="sm" margin="sm">
              <View style={{minHeight: 400}}>
                <SubcategoriesLists
                  categoriesList={d}
                  searchValue={searchValue}
                  filteredSubcategoriesData={isUserSabcategoriesLoading ? null : categories}
                  selectedCategories={selectedSubcategories}
                  onCategoryChange={value => {
                    setSelectedSubcategories(prevState =>
                      prevState.includes(value) ? prevState.filter(item => item !== value) : [...prevState, value],
                    );
                  }}
                />
              </View>
            </ModernCard>
          </Animated.View>
        </ScrollView>

        {/* Save Button */}
        <Animated.View
          entering={FadeInDown.delay(400).duration(400)}
          style={{
            paddingHorizontal: spacing.md,
            paddingBottom: Platform.OS === 'ios' ? bottom + spacing.md : spacing.md,
            backgroundColor: colors.background,
            borderTopWidth: 1,
            borderTopColor: colors.border + '40',
            paddingTop: spacing.md,
          }}>
          <ModernButton
            title={t('generic.submit')}
            onPress={submit}
            loading={isLoading}
            variant={selectedSubcategories.length === 0 ? 'outline' : 'primary'}
            size="large"
            disabled={selectedSubcategories.length === 0}
          />
        </Animated.View>
      </View>
    </>
  );
};

export default EditSubcategories;

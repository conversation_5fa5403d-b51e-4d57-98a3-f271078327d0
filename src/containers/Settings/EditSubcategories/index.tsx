import auth from '@react-native-firebase/auth';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import React, {useEffect, useLayoutEffect, useState, useCallback} from 'react';
import {useTranslation} from 'react-i18next';
import {Platform, Text, TextInput, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {organizeByCategory} from '~Utils/filterSubcategories';
import {ModernHeader} from '~components/ModernHeader';
import SubcategoriesLists from '~components/SubcategoriesLists';
import ModernButton from '~components/ModernButton';
import ModernSpinner from '~components/ModernSpinner';
import {spacing, typography} from '~constants/design';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetCategories} from '~hooks/subcategories/useGetCategories';
import {useGetSubcategories} from '~hooks/subcategories/useGetSubcategories';
import {useGetUserSubcategories} from '~hooks/user/useGetUserSubcategories';
import {useUpdateUserSubcategories} from '~hooks/user/useUpdateUserSubcategories';
import {SubCategoryType} from '~types/categories';

import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';

const EditSubcategories = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();
  const {data} = useGetSubcategories();
  const {data: d} = useGetCategories();
  const {mutateAsync: updateUserSubcategories} = useUpdateUserSubcategories();
  const {data: userSubcategories, isLoading: isUserSubcategoriesLoading} = useGetUserSubcategories(
    auth().currentUser?.uid || '',
  );
  const {goBack} = useNavigation();
  const {setIsTabBarDisabled} = useTabBar();

  const [categories, setCategories] = useState<Record<string, SubCategoryType[]> | null>(null);
  const [selectedSubcategories, setSelectedSubcategories] = useState<number[]>([]);
  const [searchValue, setSearchValue] = useState('');

  const [isLoading, setIsLoading] = useState(false);
  const isFocused = useIsFocused();
  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useLayoutEffect(() => {
    setIsTabBarDisabled(true);
  }, [setIsTabBarDisabled]);

  useEffect(() => {
    if (!searchValue) {
      setCategories(organizeByCategory([], d || [], data || []));
    }
  }, [d, data, searchValue]);

  useEffect(() => {
    if (userSubcategories) {
      setSelectedSubcategories(userSubcategories.map(item => item.subcategory_id));
    }
  }, [userSubcategories]);

  useEffect(() => {
    logScreenView('Edit Subcategories', 'EditSubcategories');
  }, []);

  const submit = async () => {
    setIsLoading(true);
    try {
      await updateUserSubcategories({ids: selectedSubcategories});
      goBack();
    } catch (E) {
      setIsLoading(false);
      console.log(E);
    }
  };

  const handleSearchCategory = useCallback(() => {
    if (searchValue?.length && data && d) {
      const inputData = data?.filter(
        (item: any) => item?.subcategory_name?.toLowerCase().indexOf(searchValue.trim().toLowerCase()) !== -1,
      );
      setCategories(organizeByCategory(selectedSubcategories, d || [], inputData || []));
    } else if (data && d) {
      setCategories(organizeByCategory(selectedSubcategories, d || [], data || []));
    }
  }, [searchValue, data, d, selectedSubcategories]);

  useEffect(() => {
    handleSearchCategory();
  }, [handleSearchCategory]);

  const handleInterestToggle = (subcategoryId: number) => {
    setSelectedSubcategories(prevState =>
      prevState.includes(subcategoryId)
        ? prevState.filter(item => item !== subcategoryId)
        : [...prevState, subcategoryId],
    );
  };

  const handleSubmit = submit;

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernHeader
        title={t('settings.edit_interests') || 'Edit Interests'}
        subtitle={
          selectedSubcategories.length > 0
            ? `${selectedSubcategories.length} ${t('settings.interests_selected') || 'interests selected'}`
            : t('settings.select_your_interests') || 'Select your interests'
        }
        variant="default"
      />

      <View style={{flex: 1, paddingTop: spacing.md}}>
        {/* Simple Search Bar */}
        <View style={{flexDirection: 'row', paddingHorizontal: spacing.md, marginBottom: spacing.md, gap: spacing.sm}}>
          <TextInput
            placeholder={t('settings.search_interests') || 'Search interests...'}
            value={searchValue}
            onChangeText={setSearchValue}
            style={{
              flex: 1,
              height: 48,
              borderWidth: 1,
              borderColor: colors.border,
              borderRadius: spacing.md,
              paddingHorizontal: spacing.md,
              fontSize: typography.fontSize.base,
              color: colors.textPrimary,
              backgroundColor: colors.surface,
            }}
            placeholderTextColor={colors.textSecondary}
          />
          <ModernButton
            title={t('generic.search') || 'Search'}
            onPress={handleSearchCategory}
            variant="primary"
            size="md"
            style={{minWidth: 80}}
          />
        </View>

        {/* Interests List */}
        <View style={{flex: 1}}>
          {isUserSubcategoriesLoading ? (
            <View style={{alignItems: 'center', justifyContent: 'center', flex: 1}}>
              <ModernSpinner size={50} />
              <Text
                style={{
                  fontSize: typography.fontSize.base,
                  color: colors.textSecondary,
                  marginTop: spacing.md,
                }}>
                {t('generic.loading') || 'Loading interests...'}
              </Text>
            </View>
          ) : categories && d ? (
            <SubcategoriesLists
              categoriesList={d}
              searchValue={searchValue}
              filteredSubcategoriesData={categories}
              selectedCategories={selectedSubcategories}
              onCategoryChange={handleInterestToggle}
              isFromSettings={true}
            />
          ) : (
            <View style={{alignItems: 'center', justifyContent: 'center', flex: 1}}>
              <Text
                style={{
                  fontSize: typography.fontSize.base,
                  color: colors.textSecondary,
                }}>
                {t('settings.no_categories') || 'No categories available'}
              </Text>
            </View>
          )}
        </View>

        {/* Bottom Save Button */}
        <View
          style={{
            paddingHorizontal: spacing.md,
            paddingBottom: Platform.OS === 'ios' ? bottom + spacing.md : spacing.md,
            paddingTop: spacing.md,
            backgroundColor: colors.background,
          }}>
          <ModernButton
            title={
              isLoading
                ? t('generic.saving') || 'Saving...'
                : selectedSubcategories.length === 0
                  ? t('settings.select_interests') || 'Select Interests'
                  : `${t('generic.save') || 'Save'} (${selectedSubcategories.length})`
            }
            onPress={handleSubmit}
            loading={isLoading}
            disabled={selectedSubcategories.length === 0 || isLoading}
            variant={selectedSubcategories.length === 0 ? 'outline' : 'primary'}
            size="lg"
            fullWidth
            hapticFeedback
            hapticType="success"
          />
        </View>
      </View>
    </View>
  );
};

export default EditSubcategories;

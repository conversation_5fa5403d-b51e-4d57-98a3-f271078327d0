import React, {useEffect, useLayoutEffect, useState, useCallback, useMemo} from 'react';
import {Platform, Text, View, ScrollView, Alert, TouchableOpacity} from 'react-native';
import {useIsFocused, useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import auth from '@react-native-firebase/auth';

import {organizeByCategory} from '~Utils/filterSubcategories';
import {ModernHeader} from '~components/ModernHeader';
import SubcategoriesLists from '~components/SubcategoriesLists';
import ModernCard from '~components/ModernCard/ModernCard';
import ModernButton from '~components/ModernButton';
import ModernTextInput from '~components/ModernTextInput';
import ModernSpinner from '~components/ModernSpinner';
import {spacing, typography, borderRadius, shadows} from '~constants/design';
import {SettingsEditInterests, BigSearchIcon, CloseIcon} from '~assets/icons';
import Animated, {FadeInDown, FadeInLeft, SlideInUp, FadeInUp} from 'react-native-reanimated';
import useTabBar from '~containers/Core/navigation/AppScreens/zustand';
import {useGetCategories} from '~hooks/subcategories/useGetCategories';
import {useGetSubcategories} from '~hooks/subcategories/useGetSubcategories';
import {useGetUserSubcategories} from '~hooks/user/useGetUserSubcategories';
import {useUpdateUserSubcategories} from '~hooks/user/useUpdateUserSubcategories';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';
import {haptics} from '~utils/haptics';
import {NavigationProps} from '~types/navigation/navigation.type';
import {SubCategoryType} from '~types/categories';
import getStyles from './styles';

// Modern Interest Chip Component
interface InterestChipProps {
  interest: SubCategoryType;
  isSelected: boolean;
  onPress: () => void;
}

const InterestChip: React.FC<InterestChipProps> = ({interest, isSelected, onPress}) => {
  const {colors} = useTheme();
  const {t} = useTranslation();

  const subcategoryName =
    t(`subcategories.${interest.subcategory_name}`) === `subcategories.${interest.subcategory_name}`
      ? interest?.subcategory_repr
      : t(`subcategories.${interest.subcategory_name}`);

  const handlePress = () => {
    haptics.light();
    onPress();
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      style={{
        backgroundColor: isSelected ? colors.primary : colors.surface,
        borderWidth: 1,
        borderColor: isSelected ? colors.primary : colors.border + '60',
        borderRadius: borderRadius.full,
        paddingHorizontal: spacing.lg,
        paddingVertical: spacing.sm,
        marginRight: spacing.sm,
        marginBottom: spacing.sm,
        flexDirection: 'row',
        alignItems: 'center',
        minHeight: 44, // Accessibility touch target
        ...shadows.sm,
      }}>
      <Text
        style={{
          fontSize: typography.fontSize.sm,
          fontWeight: typography.fontWeight.medium,
          color: isSelected ? colors.white : colors.textPrimary,
        }}>
        {subcategoryName}
      </Text>
      {isSelected && (
        <View style={{marginLeft: spacing.xs}}>
          <CloseIcon />
        </View>
      )}
    </TouchableOpacity>
  );
};

const EditSubcategories = () => {
  const {colors} = useTheme();
  const {t} = useTranslation();
  const navigation = useNavigation<NavigationProps>();
  const {bottom} = useSafeAreaInsets();
  const isFocused = useIsFocused();
  const {setIsTabBarDisabled} = useTabBar();

  // Data hooks
  const {data} = useGetSubcategories();
  const {data: d} = useGetCategories();
  const {mutateAsync: updateUserSubcategories} = useUpdateUserSubcategories();
  const {data: userSubcategories, isLoading: isUserSubcategoriesLoading} = useGetUserSubcategories(
    auth().currentUser?.uid || '',
  );

  // State management
  const [categories, setCategories] = useState<Record<string, SubCategoryType[]> | null>(null);
  const [selectedSubcategories, setSelectedSubcategories] = useState<number[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [searchDebounceTimer, setSearchDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  // Memoized selected interests for display
  const selectedInterests = useMemo(() => {
    if (!data || !selectedSubcategories.length) return [];
    return data.filter(item => selectedSubcategories.includes(item.subcategory_id));
  }, [data, selectedSubcategories]);

  // Callbacks
  const handleSaveSuccess = useCallback(() => {
    haptics.success();
    Alert.alert(
      t('settings.success') || 'Success',
      t('settings.interests_updated') || 'Your interests have been updated successfully!',
      [
        {
          text: t('generic.ok') || 'OK',
          onPress: () => navigation.goBack(),
        },
      ],
    );
  }, [navigation, t]);

  const handleSaveError = useCallback(() => {
    haptics.error();
    Alert.alert(
      t('generic.error') || 'Error',
      t('settings.interests_update_error') || 'Failed to update interests. Please try again.',
      [
        {
          text: t('generic.ok') || 'OK',
        },
      ],
    );
  }, [t]);

  const handleSearchCategory = useCallback(() => {
    if (searchValue?.length && data && d) {
      const inputData = data?.filter(
        (item: any) => item?.subcategory_name?.toLowerCase().indexOf(searchValue.trim().toLowerCase()) !== -1,
      );
      setCategories(organizeByCategory(selectedSubcategories, d, inputData));
    } else if (data && d) {
      setCategories(organizeByCategory(selectedSubcategories, d, data));
    }
  }, [searchValue, data, d, selectedSubcategories]);

  const handleSearchChange = useCallback(
    (value: string) => {
      setSearchValue(value);

      // Debounce search
      if (searchDebounceTimer) {
        clearTimeout(searchDebounceTimer);
      }

      const timer = setTimeout(() => {
        handleSearchCategory();
      }, 300);

      setSearchDebounceTimer(timer);
    },
    [searchDebounceTimer, handleSearchCategory],
  );

  const handleInterestToggle = useCallback((subcategoryId: number) => {
    haptics.light();
    setSelectedSubcategories(prevState =>
      prevState.includes(subcategoryId)
        ? prevState.filter(item => item !== subcategoryId)
        : [...prevState, subcategoryId],
    );
  }, []);

  const handleSubmit = useCallback(async () => {
    setIsLoading(true);
    try {
      await updateUserSubcategories({ids: selectedSubcategories});
      handleSaveSuccess();
    } catch (error) {
      console.error('Error updating interests:', error);
      handleSaveError();
    } finally {
      setIsLoading(false);
    }
  }, [selectedSubcategories, updateUserSubcategories, handleSaveSuccess, handleSaveError]);

  // Effects
  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useLayoutEffect(() => {
    setIsTabBarDisabled(true);
  }, [setIsTabBarDisabled]);

  useEffect(() => {
    if (d && data && !searchValue) {
      setCategories(organizeByCategory(selectedSubcategories, d, data));
    }
  }, [d, data, searchValue, selectedSubcategories]);

  useEffect(() => {
    if (userSubcategories) {
      setSelectedSubcategories(userSubcategories.map(item => item.subcategory_id));
    }
  }, [userSubcategories]);

  useEffect(() => {
    logScreenView('Edit Subcategories', 'EditSubcategories');
  }, []);

  useEffect(() => {
    return () => {
      if (searchDebounceTimer) {
        clearTimeout(searchDebounceTimer);
      }
    };
  }, [searchDebounceTimer]);

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernHeader
        title={t('settings.edit_interests') || 'Edit Interests'}
        subtitle={`${selectedSubcategories.length} ${t('settings.interests_selected') || 'interests selected'}`}
        variant="default"
      />
      <ScrollView
        style={{flex: 1}}
        contentContainerStyle={{
          paddingBottom: spacing['6xl'] + bottom,
        }}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled">
        {/* Header Section */}
        <Animated.View entering={FadeInDown.delay(100).duration(400)}>
          <ModernCard variant="elevated" padding="xl" margin="md" style={{alignItems: 'center'}}>
            <SettingsEditInterests />
            <Text
              style={{
                fontSize: typography.fontSize['2xl'],
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                marginTop: spacing.md,
                textAlign: 'center',
              }}>
              {t('settings.edit_interests') || 'Edit Interests'}
            </Text>
            <Text
              style={{
                fontSize: typography.fontSize.base,
                fontWeight: typography.fontWeight.normal,
                color: colors.textSecondary,
                lineHeight: typography.fontSize.base * 1.5,
                marginTop: spacing.sm,
                textAlign: 'center',
              }}>
              {t('settings.interests_description') ||
                'Select your interests to get personalized event recommendations.'}
            </Text>
          </ModernCard>
        </Animated.View>

        {/* Selected Interests Section */}
        {selectedInterests.length > 0 && (
          <Animated.View entering={FadeInDown.delay(150).duration(400)}>
            <ModernCard variant="default" padding="lg" margin="md">
              <Text
                style={{
                  fontSize: typography.fontSize.lg,
                  fontWeight: typography.fontWeight.bold,
                  color: colors.textPrimary,
                  marginBottom: spacing.md,
                }}>
                {t('settings.selected_interests') || 'Selected Interests'} ({selectedInterests.length})
              </Text>
              <View style={{flexDirection: 'row', flexWrap: 'wrap'}}>
                {selectedInterests.map((interest, index) => (
                  <Animated.View key={interest.subcategory_id} entering={FadeInLeft.delay(index * 50).duration(300)}>
                    <InterestChip
                      interest={interest}
                      isSelected={true}
                      onPress={() => handleInterestToggle(interest.subcategory_id)}
                    />
                  </Animated.View>
                ))}
              </View>
            </ModernCard>
          </Animated.View>
        )}

        {/* Search Section */}
        <Animated.View entering={FadeInDown.delay(200).duration(400)}>
          <ModernCard variant="default" padding="lg" margin="md">
            <Text
              style={{
                fontSize: typography.fontSize.lg,
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                marginBottom: spacing.md,
              }}>
              {t('settings.discover_interests') || 'Discover Interests'}
            </Text>

            <ModernTextInput
              label={t('generic.search') || 'Search'}
              placeholder={t('settings.search_interests') || 'Search interests...'}
              value={searchValue}
              onChangeText={handleSearchChange}
              leftIcon={<BigSearchIcon />}
              rightIcon={
                searchValue ? (
                  <TouchableOpacity onPress={() => handleSearchChange('')}>
                    <CloseIcon />
                  </TouchableOpacity>
                ) : undefined
              }
              variant="outlined"
              size="lg"
              style={{marginBottom: spacing.sm}}
            />
          </ModernCard>
        </Animated.View>

        {/* Interests List */}
        <Animated.View entering={FadeInDown.delay(300).duration(400)}>
          <ModernCard variant="default" padding="sm" margin="md">
            {isUserSubcategoriesLoading ? (
              <View style={{alignItems: 'center', justifyContent: 'center', minHeight: 200}}>
                <ModernSpinner size={50} />
                <Text
                  style={{
                    fontSize: typography.fontSize.base,
                    color: colors.textSecondary,
                    marginTop: spacing.md,
                  }}>
                  {t('generic.loading') || 'Loading interests...'}
                </Text>
              </View>
            ) : (
              <View style={{minHeight: 400}}>
                <SubcategoriesLists
                  categoriesList={d}
                  searchValue={searchValue}
                  filteredSubcategoriesData={categories}
                  selectedCategories={selectedSubcategories}
                  onCategoryChange={handleInterestToggle}
                  isFromSettings={true}
                />
              </View>
            )}
          </ModernCard>
        </Animated.View>
      </ScrollView>

      {/* Floating Save Button */}
      <Animated.View
        entering={SlideInUp.delay(400).duration(400)}
        style={{
          position: 'absolute',
          bottom: Platform.OS === 'ios' ? bottom + spacing.md : spacing.md,
          left: spacing.md,
          right: spacing.md,
          backgroundColor: colors.background,
          borderRadius: borderRadius.xl,
          ...shadows.lg,
        }}>
        <ModernButton
          title={
            isLoading
              ? t('generic.saving') || 'Saving...'
              : selectedSubcategories.length === 0
                ? t('settings.select_interests') || 'Select Interests'
                : `${t('generic.save') || 'Save'} (${selectedSubcategories.length})`
          }
          onPress={handleSubmit}
          loading={isLoading}
          disabled={selectedSubcategories.length === 0 || isLoading}
          variant={selectedSubcategories.length === 0 ? 'outline' : 'primary'}
          size="lg"
          fullWidth
          hapticFeedback
          hapticType="success"
        />
      </Animated.View>
    </View>
  );
};

export default EditSubcategories;

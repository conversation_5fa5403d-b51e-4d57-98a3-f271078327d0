import auth from '@react-native-firebase/auth';
import {useNavigation} from '@react-navigation/native';
import {Formik} from 'formik';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, KeyboardAvoidingView, Platform, Text, View, ScrollView} from 'react-native';
import * as yup from 'yup';
import {CustomTextInput} from '~components/CustomTextInput';
import {ModernHeader} from '~components/ModernHeader';
import ModernCard from '~components/ModernCard/ModernCard';
import ModernButton from '~components/ModernButton';
import {useIsFocused} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius, shadows} from '~constants/design';
import Animated, {FadeInDown, SlideInUp} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const ChangePassword = () => {
  const {colors} = useTheme();
  const navigation = useNavigation();
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Change Password', 'ChangePassword');
  }, []);

  const handlePasswordReset = async ({email}: {email: string}) => {
    try {
      setIsLoading(true);
      await auth().sendPasswordResetEmail(email.trim());
      Alert.alert(
        t('settings.reset_email_sent') || 'Reset Email Sent',
        t('settings.reset_email_message') || 'Please check your email for password reset instructions.',
        [
          {
            text: t('generic.ok') || 'OK',
            onPress: () => navigation.goBack(),
          },
        ],
      );
    } catch (error: any) {
      setIsLoading(false);
      if (error.message) {
        Alert.alert(t('generic.error') || 'Error', error.message as string);
      }
    }
  };

  const validationSchema = yup.object().shape({
    email: yup
      .string()
      .required(t('settings.email_required') || 'Please enter your email')
      .email(t('settings.email_invalid') || 'Enter valid email'),
  });

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernHeader
        title={t('settings.change_password') || 'Change Password'}
        subtitle={t('settings.change_password_subtitle') || 'Reset your account password'}
        variant="default"
      />

      <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView
          style={{flex: 1}}
          contentContainerStyle={{
            paddingTop: spacing.md,
            paddingBottom: spacing['6xl'] + bottom,
          }}
          showsVerticalScrollIndicator={false}>
          {/* Header Section */}
          <Animated.View entering={FadeInDown.delay(100).duration(400)}>
            <ModernCard variant="elevated" padding="xl" margin="md" style={{alignItems: 'center'}}>
              <View
                style={{
                  width: 80,
                  height: 80,
                  borderRadius: 40,
                  backgroundColor: colors.primary + '20',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginBottom: spacing.md,
                }}>
                <Text style={{fontSize: 32}}>🔐</Text>
              </View>
              <Text
                style={{
                  fontSize: typography.fontSize['2xl'],
                  fontWeight: typography.fontWeight.bold,
                  color: colors.textPrimary,
                  marginBottom: spacing.sm,
                  textAlign: 'center',
                }}>
                {t('settings.pwd_header') || 'Change Password'}
              </Text>
              <Text
                style={{
                  fontSize: typography.fontSize.base,
                  fontWeight: typography.fontWeight.normal,
                  color: colors.textSecondary,
                  lineHeight: typography.fontSize.base * 1.5,
                  textAlign: 'center',
                }}>
                {t('settings.pwd_requirements') || 'Enter your email address to receive password reset instructions.'}
              </Text>
            </ModernCard>
          </Animated.View>

          {/* Password Reset Form */}
          <Animated.View entering={FadeInDown.delay(200).duration(400)}>
            <ModernCard variant="default" padding="xl" margin="md">
              <Text
                style={{
                  fontSize: typography.fontSize.lg,
                  fontWeight: typography.fontWeight.semibold,
                  color: colors.textPrimary,
                  marginBottom: spacing.md,
                }}>
                {t('settings.reset_password') || 'Reset Password'}
              </Text>

              <Formik
                validationSchema={validationSchema}
                onSubmit={handlePasswordReset}
                initialValues={{
                  email: '',
                }}>
                {({values, handleChange, handleSubmit, errors}) => (
                  <>
                    <CustomTextInput
                      value={values.email}
                      onChangeValue={handleChange('email')}
                      description={t('settings.email')}
                      errorText={errors.email?.toString()}
                      placeholder={t('settings.email_placeholder') || 'Enter your email address'}
                      keyboardType="email-address"
                      autoCapitalize="none"
                      autoCorrect={false}
                    />

                    <View style={{marginTop: spacing.lg}}>
                      <ModernButton
                        title={t('settings.send_reset_email') || 'Send Reset Email'}
                        onPress={handleSubmit}
                        loading={isLoading}
                        variant="primary"
                        size="lg"
                        fullWidth
                        hapticFeedback
                        hapticType="success"
                      />
                    </View>
                  </>
                )}
              </Formik>
            </ModernCard>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default ChangePassword;

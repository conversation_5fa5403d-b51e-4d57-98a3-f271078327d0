import auth from '@react-native-firebase/auth';
import {useNavigation} from '@react-navigation/native';
import {Formik} from 'formik';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Alert, KeyboardAvoidingView, Platform, Text, View, ScrollView} from 'react-native';
import * as yup from 'yup';
import {CustomTextInput} from '~components/CustomTextInput';
import {GoBackHeader} from '~components/GoBackHeader';
import ModernCard from '~components/ModernCard/ModernCard';
import ModernButton from '~components/ModernButton';
import {useIsFocused} from '@react-navigation/native';
import {useEffect, useState} from 'react';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography} from '~constants/design';
import Animated, {FadeInDown} from 'react-native-reanimated';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const ChangePassword = () => {
  const {colors} = useTheme();
  const navigation = useNavigation();
  const {t} = useTranslation();
  const {bottom} = useSafeAreaInsets();

  const [isLoading, setIsLoading] = useState(false);

  const isFocused = useIsFocused();

  useEffect(() => {
    setIsLoading(false);
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Change Password', 'ChangePassword');
  }, []);

  const handlePasswordReset = async ({email}: {email: string}) => {
    try {
      setIsLoading(true);
      await auth().sendPasswordResetEmail(email.trim());
      Alert.alert(
        t('settings.reset_email_sent') || 'Reset Email Sent',
        t('settings.reset_email_message') || 'Please check your email for password reset instructions.',
        [
          {
            text: t('generic.ok') || 'OK',
            onPress: () => navigation.goBack(),
          },
        ],
      );
    } catch (error: any) {
      setIsLoading(false);
      if (error.message) {
        Alert.alert(t('generic.error') || 'Error', error.message as string);
      }
    }
  };

  const validationSchema = yup.object().shape({
    email: yup
      .string()
      .required(t('settings.email_required') || 'Please enter your email')
      .email(t('settings.email_invalid') || 'Enter valid email'),
  });

  return (
    <>
      <GoBackHeader />
      <View style={{flex: 1, backgroundColor: colors.background}}>
        <KeyboardAvoidingView style={{flex: 1}} behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
          <ScrollView
            style={{flex: 1}}
            contentContainerStyle={{
              paddingHorizontal: spacing.md,
              paddingTop: spacing.xl,
              paddingBottom: spacing['6xl'],
            }}
            showsVerticalScrollIndicator={false}>
            {/* Header */}
            <Animated.View entering={FadeInDown.delay(100).duration(400)}>
              <Text
                style={{
                  fontSize: typography.fontSize['3xl'],
                  fontWeight: typography.fontWeight.bold,
                  color: colors.textPrimary,
                  marginBottom: spacing.md,
                }}>
                {t('settings.pwd_header')}
              </Text>
              <Text
                style={{
                  fontSize: typography.fontSize.base,
                  fontWeight: typography.fontWeight.normal,
                  color: colors.textSecondary,
                  lineHeight: typography.fontSize.base * 1.5,
                  marginBottom: spacing.xl,
                }}>
                {t('settings.pwd_requirements')}
              </Text>
            </Animated.View>

            {/* Password Reset Form */}
            <Animated.View entering={FadeInDown.delay(200).duration(400)}>
              <ModernCard variant="default" padding="lg" margin="sm">
                <Text
                  style={{
                    fontSize: typography.fontSize.lg,
                    fontWeight: typography.fontWeight.semibold,
                    color: colors.textPrimary,
                    marginBottom: spacing.md,
                  }}>
                  {t('settings.reset_password') || 'Reset Password'}
                </Text>

                <Formik
                  validationSchema={validationSchema}
                  onSubmit={handlePasswordReset}
                  initialValues={{
                    email: '',
                  }}>
                  {({values, handleChange, handleSubmit, errors}) => (
                    <>
                      <CustomTextInput
                        value={values.email}
                        onChangeValue={handleChange('email')}
                        description={t('settings.email')}
                        errorText={errors.email?.toString()}
                        placeholder={t('settings.email_placeholder') || 'Enter your email address'}
                        keyboardType="email-address"
                        autoCapitalize="none"
                        autoCorrect={false}
                      />

                      <View style={{marginTop: spacing.lg}}>
                        <ModernButton
                          title={t('settings.send_reset_email') || 'Send Reset Email'}
                          onPress={handleSubmit}
                          loading={isLoading}
                          variant="primary"
                          size="large"
                        />
                      </View>
                    </>
                  )}
                </Formik>
              </ModernCard>
            </Animated.View>
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </>
  );
};

export default ChangePassword;

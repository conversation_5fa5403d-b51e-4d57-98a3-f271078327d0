import React, {useEffect, useState} from 'react';
import {View, Text, FlatList, StyleSheet, Image, SafeAreaView, ActivityIndicator, TouchableOpacity} from 'react-native';
import {useNavigation, useIsFocused} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {GoBackHeader} from '~components/GoBackHeader';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useGetOrderList} from '~hooks/event/useGetOrderList';
import {OrderDetail} from '~types/api/event';
import moment from 'moment';
import {SCREENS} from '~constants';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useTheme} from '~contexts/ThemeContext';

const PurchaseHistory = () => {
  const {colors} = useTheme();
  const navigation = useNavigation<NavigationProps>();
  const {t} = useTranslation();
  const isFocused = useIsFocused();
  const {data: orderList, isLoading, refetch} = useGetOrderList();

  useEffect(() => {
    refetch();
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Purchase History', 'PurchaseHistory');
  }, []);

  const getStatusColor = (status: string) => {
    if (status == 'succeeded') {
      return colors.statusGreen;
    } else if (status == 'canceled') {
      return colors.error;
    } else {
      return colors.warning;
    }
  };

  const onOrderClick = (order_id: string) => {
    navigation.navigate(SCREENS.PAYMENT_SUCCESS, {
      order_id: order_id,
    });
  };

  const renderEventItem = ({item}: {item: OrderDetail; index: number}) => (
    <TouchableOpacity style={styles.card} onPress={() => onOrderClick(item.order_id)}>
      <Image resizeMode={'cover'} source={{uri: item.event.image_url}} style={styles.image} />
      <View style={styles.details}>
        <Text style={styles.title}>{item.event.name}</Text>
        <Text style={styles.date}>
          📅 {t('events.start_date')}: {moment(item.event.start_date).format('DD MMM YYYY HH:mm a')}
        </Text>
        <Text style={styles.date}>
          🕒 {t('events.end_date')}: {moment(item.event.end_date).format('DD MMM YYYY HH:mm a')}
        </Text>
        <Text style={styles.info}>
          💰 {t('cost')}: <Text style={styles.highlight}>{`${item.currency} ${item.total_amount}`}</Text>
        </Text>
        <Text style={styles.info}>
          🎟 {t('status')}:{' '}
          <Text style={[styles.highlight, {color: getStatusColor(item.status), textTransform: 'capitalize'}]}>
            {item.status}
          </Text>
        </Text>
      </View>
    </TouchableOpacity>
  );

  const styles = StyleSheet.create({
    purhaseText: {
      color: colors.black,
    },
    emptyList: {
      height: 300,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
    loaderContainer: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      alignItems: 'center',
      justifyContent: 'center',
    },
    safeArea: {
      flex: 1,
      backgroundColor: colors.background,
    },
    container: {
      flex: 1,
      padding: 16,
    },
    header: {
      fontSize: 22,
      fontWeight: '700',
      marginBottom: 16,
      textAlign: 'center',
      color: colors.textPrimary,
    },
    card: {
      flexDirection: 'row',
      backgroundColor: colors.white,
      borderRadius: 10,
      marginBottom: 12,
      padding: 12,
      shadowColor: colors.black,
      shadowOffset: {width: 0, height: 4},
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 4,
    },
    image: {
      width: 90,
      height: 120,
      borderRadius: 8,
      marginRight: 12,
    },
    details: {
      flex: 1,
      justifyContent: 'space-between',
    },
    title: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.textPrimary,
      marginBottom: 6,
    },
    date: {
      fontSize: 11,
      color: colors.textSecondary,
      marginBottom: 8,
    },
    info: {
      fontSize: 11,
      color: colors.textSecondary,
      marginBottom: 8,
    },
    highlight: {
      fontWeight: '700',
      color: colors.eventInfluencer,
    },
  });

  return (
    <SafeAreaView style={styles.safeArea}>
      <GoBackHeader />
      <View style={styles.container}>
        <Text style={styles.header}>{t('settings.purchase_history')}</Text>
        <FlatList
          data={orderList}
          keyExtractor={item => item.order_id}
          renderItem={renderEventItem}
          ListEmptyComponent={() => {
            if (isLoading) {
              return null;
            }
            return (
              <View style={styles.emptyList}>
                <Text style={styles.purhaseText}>You haven’t made any purchases yet.</Text>
              </View>
            );
          }}
          showsVerticalScrollIndicator={false}
        />
      </View>
      {isLoading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size={'large'} />
        </View>
      )}
    </SafeAreaView>
  );
};

export default PurchaseHistory;

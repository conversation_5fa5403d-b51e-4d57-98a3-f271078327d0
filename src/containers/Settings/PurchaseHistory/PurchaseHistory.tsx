import React, {useEffect, useState} from 'react';
import {View, Text, FlatList, StyleSheet, Image, SafeAreaView, ActivityIndicator, TouchableOpacity} from 'react-native';
import {useNavigation, useIsFocused} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {ModernHeader} from '~components/ModernHeader';
import ModernCard from '~components/ModernCard/ModernCard';
import ModernSpinner from '~components/ModernSpinner';
import {logScreenView} from '~Utils/firebaseAnalytics';
import {useGetOrderList} from '~hooks/event/useGetOrderList';
import {OrderDetail} from '~types/api/event';
import moment from 'moment';
import {SCREENS} from '~constants';
import {NavigationProps} from '~types/navigation/navigation.type';
import {useTheme} from '~contexts/ThemeContext';
import {spacing, typography, borderRadius, shadows} from '~constants/design';
import Animated, {FadeInDown, FadeInLeft} from 'react-native-reanimated';

const PurchaseHistory = () => {
  const {colors} = useTheme();
  const navigation = useNavigation<NavigationProps>();
  const {t} = useTranslation();
  const isFocused = useIsFocused();
  const {data: orderList, isLoading, refetch} = useGetOrderList();

  useEffect(() => {
    refetch();
  }, [isFocused]);

  useEffect(() => {
    logScreenView('Purchase History', 'PurchaseHistory');
  }, []);

  const getStatusColor = (status: string) => {
    if (status == 'succeeded') {
      return colors.statusGreen;
    } else if (status == 'canceled') {
      return colors.error;
    } else {
      return colors.warning;
    }
  };

  const onOrderClick = (order_id: string) => {
    navigation.navigate(SCREENS.PAYMENT_SUCCESS, {
      order_id: order_id,
    });
  };

  const renderEventItem = ({item, index}: {item: OrderDetail; index: number}) => (
    <Animated.View entering={FadeInLeft.delay(index * 100).duration(400)}>
      <ModernCard variant="default" padding="md" margin="sm">
        <TouchableOpacity
          style={{flexDirection: 'row'}}
          onPress={() => onOrderClick(item.order_id)}
          activeOpacity={0.7}>
          <Image
            resizeMode="cover"
            source={{uri: item.event.image_url}}
            style={{
              width: 80,
              height: 80,
              borderRadius: borderRadius.md,
              marginRight: spacing.md,
            }}
          />
          <View style={{flex: 1}}>
            <Text
              style={{
                fontSize: typography.fontSize.lg,
                fontWeight: typography.fontWeight.bold,
                color: colors.textPrimary,
                marginBottom: spacing.xs,
              }}>
              {item.event.name}
            </Text>
            <Text
              style={{
                fontSize: typography.fontSize.sm,
                color: colors.textSecondary,
                marginBottom: spacing.xs,
              }}>
              📅 {moment(item.event.start_date).format('DD MMM YYYY HH:mm')}
            </Text>
            <Text
              style={{
                fontSize: typography.fontSize.sm,
                color: colors.textSecondary,
                marginBottom: spacing.xs,
              }}>
              💰 {item.currency} {item.total_amount}
            </Text>
            <View
              style={{
                backgroundColor: getStatusColor(item.status) + '20',
                paddingHorizontal: spacing.sm,
                paddingVertical: spacing.xs,
                borderRadius: borderRadius.sm,
                alignSelf: 'flex-start',
              }}>
              <Text
                style={{
                  fontSize: typography.fontSize.xs,
                  fontWeight: typography.fontWeight.semibold,
                  color: getStatusColor(item.status),
                  textTransform: 'capitalize',
                }}>
                {item.status}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      </ModernCard>
    </Animated.View>
  );

  const styles = StyleSheet.create({
    purhaseText: {
      color: colors.black,
    },
    emptyList: {
      height: 300,
      width: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
    loaderContainer: {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
      alignItems: 'center',
      justifyContent: 'center',
    },
    safeArea: {
      flex: 1,
      backgroundColor: colors.background,
    },
    container: {
      flex: 1,
      padding: 16,
    },
    header: {
      fontSize: 22,
      fontWeight: '700',
      marginBottom: 16,
      textAlign: 'center',
      color: colors.textPrimary,
    },
    card: {
      flexDirection: 'row',
      backgroundColor: colors.white,
      borderRadius: 10,
      marginBottom: 12,
      padding: 12,
      shadowColor: colors.black,
      shadowOffset: {width: 0, height: 4},
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 4,
    },
    image: {
      width: 90,
      height: 120,
      borderRadius: 8,
      marginRight: 12,
    },
    details: {
      flex: 1,
      justifyContent: 'space-between',
    },
    title: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.textPrimary,
      marginBottom: 6,
    },
    date: {
      fontSize: 11,
      color: colors.textSecondary,
      marginBottom: 8,
    },
    info: {
      fontSize: 11,
      color: colors.textSecondary,
      marginBottom: 8,
    },
    highlight: {
      fontWeight: '700',
      color: colors.eventInfluencer,
    },
  });

  return (
    <View style={{flex: 1, backgroundColor: colors.background}}>
      <ModernHeader
        title={t('settings.purchase_history') || 'Purchase History'}
        subtitle={
          orderList?.length
            ? `${orderList.length} ${t('settings.orders_found') || 'orders found'}`
            : t('settings.your_orders') || 'Your orders'
        }
        variant="elevated"
        backgroundColor={colors.surface}
      />
      <View style={styles.container}>
        <Text style={styles.header}>{t('settings.purchase_history')}</Text>
        <FlatList
          data={orderList}
          keyExtractor={item => item.order_id}
          renderItem={renderEventItem}
          ListEmptyComponent={() => {
            if (isLoading) {
              return null;
            }
            return (
              <View style={styles.emptyList}>
                <Text style={styles.purhaseText}>You haven’t made any purchases yet.</Text>
              </View>
            );
          }}
          showsVerticalScrollIndicator={false}
        />
      </View>
      {isLoading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size={'large'} />
        </View>
      )}
    </View>
  );
};

export default PurchaseHistory;
